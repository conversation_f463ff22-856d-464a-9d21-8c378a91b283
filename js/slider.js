// Image Slider JavaScript with Fade Effect
document.addEventListener('DOMContentLoaded', function() {
  const slides = document.querySelectorAll('.slide');
  
  let currentSlide = 0;
  const totalSlides = slides.length;
  
  // Initialize slider
  function initSlider() {
    updateSlider();
    startAutoSlide();
  }
  
  // Update slider with fade effect
  function updateSlider() {
    // Remove active class from all slides
    slides.forEach(slide => {
      slide.classList.remove('active');
    });
    
    // Add active class to current slide
    slides[currentSlide].classList.add('active');
  }
  
  // Next slide
  function nextSlide() {
    currentSlide = (currentSlide + 1) % totalSlides;
    updateSlider();
  }
  
  // Auto slide with fade transition
  function startAutoSlide() {
    setInterval(() => {
      nextSlide();
    }, 4000); // Change slide every 4 seconds
  }
  
  // Initialize the slider
  initSlider();
  
  // Add smooth entrance animation
  setTimeout(() => {
    document.querySelector('.slider-container').style.opacity = '1';
  }, 1000);
}); 
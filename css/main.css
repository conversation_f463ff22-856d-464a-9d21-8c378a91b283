.more-pens {
  position: fixed;
  left: 20px;
  bottom: 20px;
  z-index: 10;
  font-family: "Montserrat";
  font-size: 12px;
}

a.white-mode, a.white-mode:link, a.white-mode:visited, a.white-mode:active {
  font-family: "Montserrat";
  font-size: 12px;
  text-decoration: none;        /* background: #212121; */
  padding: 4px 8px;
  color: #f7f7f7;
}

a.white-mode:hover, a.white-mode:link:hover, a.white-mode:visited:hover, a.white-mode:active:hover {
  background: #edf3f8;
  color: #212121;
}

.title {
  z-index: 9999 !important;
  position: absolute;
  left: 50%;
  top: 42%;
  transform: translateX(-50%) translateY(-50%);
  font-family: "Montserrat";
  text-align: center;
  width: 100%;
}

.title h1 {
  z-index: 99;
  position: relative;
  color: #fff;
  font-weight: 100;
  font-size: 70px;
  padding: 0;
  margin: 0;
  line-height: 1;
  text-shadow: 0 0 10px #ff006c, 0 0 20px #ff006c, 0 0 30px #ff006c, 0 0 40px #ff417d, 0 0 70px #ff417d, 0 0 80px #ff417d, 0 0 100px #ff417d, 0 0 150px #ff417d;
}

.title h1 span {
  z-index: 99;
  font-weight: 600;
  padding: 0;
  margin: 0;
  color: #ffffff;
}

.title h3 {
  z-index: 99;
  font-weight: 200;
  font-size: 26px;
  padding: 0;
  margin: 0;
  line-height: 1;
  color: #ffffff;
  letter-spacing: 2px;
}

/* 爱心css */
canvas {
  position: absolute;
  width: 100%;
  height: 100%;
}

.img {
  position: absolute;
  left: 50%;
  top: 60%;
  transform: translate(-50%, -50%);
  width: 420px;
  height: 420px;
}

#pinkboard {
  position: relative;
  top: 0%;
  left: 0%;
  height: 429px;
}

.STARDUST1 {
  position: relative !important;
  top: -60px;
}

.STARDUST2 {
  position: relative !important;
  top: -40px;
}

.STARDUST3 {
  position: relative !important;
  top: -20px;
}

/* 星空css */
html, body {
  padding: 0px;
  margin: 0px;
  width: 100%;
  height: 100%;
  position: fixed;
}

body {
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-filter: contrast(120%);
  filter: contrast(120%);
  background-image: radial-gradient(1600px at 70% 120%, rgba(33, 39, 80, 1) 10%, #020409 100%) !important;        /* background-color: black; */
}

.container2 {

  /* z-index: 8; */
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(1600px at 70% 120%, rgba(33, 39, 80, 1) 10%, #020409 100%) !important;
}

.content {
  width: inherit;
  height: inherit;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(1600px at 70% 120%, rgba(33, 39, 80, 1) 10%, #020409 100%) !important;
}

#universe {
  width: 100%;
  height: 100%;
}

#footerContent {
  font-family: sans-serif;
  font-size: 110%;
  color: rgba(200, 220, 255, 0.3);
  width: 100%;
  position: fixed;
  bottom: 0px;
  padding: 20px;
  text-align: center;
  z-index: 20;
}

/* #footer {
  position: absolute;
  bottom: 0px;
  height: 300px;
  width: 100%;
} */
#scene {
  height: 100%;
  position: absolute;
  left: 50%;
  margin-left: -800px;
}

a {
  text-decoration: none;
  color: rgba(200, 220, 255, 1);
  opacity: 0.4;
  transition: opacity 0.4s ease;
}

a:hover {
  opacity: 1;
}

/* heart image: https://stackoverflow.com/a/51216413 */
img {

  /* width: 200px; */
  aspect-ratio: 1;
  object-fit: cover;
  --_m: radial-gradient(#000 69%, #0000 70%) 84.5% fill/100%;
  -webkit-mask-box-image: var(--_m);
  mask-border: var(--_m);
  clip-path: polygon(-41% 0, 50% 91%, 141% 0);
}

/* Heartbeat animation for the heart symbol ❤ */
.title h3 strong {
  animation: heartbeat-symbol 1.5s ease-in-out infinite;
  display: inline-block;
  text-shadow: 0 0 10px #ff006c, 0 0 20px #ff006c, 0 0 30px #ff006c;
}

/* Heartbeat animation for heart symbol */
@keyframes heartbeat-symbol {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
  100% {
    transform: scale(1);
  }
}

/* Image Slider Styles */
.slider-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  overflow: hidden;
  opacity: 0;
  transition: all 0.8s ease;
}

.slider {
  position: relative;
  width: 100%;
  height: 100%;
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 1s ease-in-out;
}

.slide img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  transition: transform 0.3s ease;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.slide.active {
  opacity: 1;
}

.slide.active .slide-content {
  opacity: 1;
}

.slide:hover img {
  transform: scale(1.05);
}

.slide-content {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 15px 25px;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  text-align: center;
  min-width: 200px;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
  transition-delay: 0.3s;
}

.slide:hover img {
  transform: scale(1.1);
}

.slide-content h2 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.slide-content p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

/* Fade transition styles */
.slide.active {
  opacity: 1;
  z-index: 2;
}

.slide:not(.active) {
  opacity: 0;
  z-index: 1;
}

/* Responsive design */
@media (max-width: 900px) {
  .slide img {
    width: 80px;
    height: 80px;
  }
  
  .slide-content h2 {
    font-size: 16px;
  }
  
  .slide-content p {
    font-size: 12px;
  }
}

@media (max-width: 600px) {
  .slide img {
    width: 60px;
    height: 60px;
  }
  
  .slider-btn {
    padding: 10px 15px;
    font-size: 18px;
  }
  
  .slide-content h2 {
    font-size: 14px;
  }
  
  .slide-content p {
    font-size: 10px;
  }
}
